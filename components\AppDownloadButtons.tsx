import Image from "next/image";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { APP_CONFIG, getDownloadOptions } from "@/lib/app-config";
import SignUpPlaystorePopover from "@/components/SignUpPlaystorePopover";

type AppDownloadButtonsProps = {
  className?: string;
  variant?: "badges" | "text";
  showApk?: boolean;
  size?: "sm" | "md" | "lg";
};

const AppDownloadButtons = ({
  className,
  variant = "badges",
  showApk = true,
  size = "md",
}: AppDownloadButtonsProps) => {
  const downloadOptions = getDownloadOptions();
  const filteredOptions = showApk
    ? downloadOptions
    : downloadOptions.filter((option) => option !== APP_CONFIG.apk);

  if (variant === "text") {
    return (
      <div className={cn("flex flex-wrap gap-4", className)}>
        <Link
          href={APP_CONFIG.appStore.url}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center justify-center rounded-md bg-black px-4 py-2 text-white transition-colors hover:bg-gray-800"
        >
          App Store
        </Link>
        <SignUpPlaystorePopover>
          <button className="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700">
            Google Play
          </button>
        </SignUpPlaystorePopover>
        {showApk && (
          <Link
            href={APP_CONFIG.apk.url}
            className="inline-flex items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          >
            Download APK
          </Link>
        )}
      </div>
    );
  }

  const sizeClasses = {
    sm: "max-h-[2rem] max-w-[10rem]",
    md: "max-h-[3rem] max-w-[12rem]",
    lg: "max-h-[4rem] max-w-[16rem]",
  };

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {filteredOptions.map((option) => {
        // Special handling for Google Play button
        if (option === APP_CONFIG.googlePlay) {
          return (
            <SignUpPlaystorePopover
              key={option.url}
              className={cn(
                "flex justify-center transition-transform hover:scale-105",
                sizeClasses[size],
              )}
            >
              <button
                className={cn(
                  "flex cursor-pointer justify-center transition-transform hover:scale-105",
                  sizeClasses[size],
                )}
              >
                <Image
                  src={option.badge}
                  alt={option.alt}
                  width={192}
                  height={192}
                  className="h-full w-full object-contain"
                />
              </button>
            </SignUpPlaystorePopover>
          );
        }

        // Regular handling for other options
        return (
          <Link
            href={option.url}
            key={option.url}
            target={option.url.startsWith("http") ? "_blank" : "_self"}
            rel={
              option.url.startsWith("http") ? "noopener noreferrer" : undefined
            }
            className={cn(
              "flex justify-center transition-transform hover:scale-105",
              sizeClasses[size],
            )}
          >
            <Image
              src={option.badge}
              alt={option.alt}
              width={192}
              height={192}
              className="h-full w-full object-contain"
            />
          </Link>
        );
      })}
    </div>
  );
};

export default AppDownloadButtons;
