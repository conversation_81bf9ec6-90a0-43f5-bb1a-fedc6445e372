"use client";

import { useState, useTransition } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { submitBetaSignup } from "@/app/(site)/actions";

type SignUpPlaystorePopoverProps = {
  children: React.ReactNode;
  className?: string;
};

// Email validation schema
const emailSchema = z.object({
  email: z
    .string()
    .email("Email inválido")
    .refine(
      (email) => email.endsWith("@gmail.com"),
      "Apenas endereços Gmail são aceitos (@gmail.com)",
    ),
});

const SignUpPlaystorePopover = ({
  children,
  className,
}: SignUpPlaystorePopoverProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setEmailError(null);

    // Validate email with zod
    const validation = emailSchema.safeParse({ email });
    if (!validation.success) {
      setEmailError(validation.error.errors[0]?.message || "Email inválido");
      return;
    }

    startTransition(async () => {
      try {
        const result = await submitBetaSignup(email);

        if (result.success) {
          toast.success(result.message);
          setEmail("");
          setIsPopoverOpen(false);
        } else {
          toast.error(result.message);
          if (result.error === "VALIDATION_ERROR") {
            setEmailError(result.message);
          }
        }
      } catch (error) {
        toast.error("Erro inesperado. Tente novamente.");
      }
    });
  };

  return (
    <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
      <PopoverTrigger asChild className={cn(className)}>
        {children}
      </PopoverTrigger>
      <PopoverContent className={cn("w-80 p-4")} align="center">
        <div className={cn("space-y-4")}>
          <div className={cn("space-y-2")}>
            <h3 className={cn("text-lg font-semibold")}>
              Acesso Beta - Google Play
            </h3>
            <p className={cn("text-muted-foreground text-sm")}>
              Insira seu endereço Gmail para receber acesso ao teste beta da
              nossa app na Play Store.
            </p>
          </div>
          <form onSubmit={handleSubmit} className={cn("space-y-3")}>
            <div className={cn("space-y-2")}>
              <Label htmlFor="gmail-input">Endereço Gmail</Label>
              <Input
                id="gmail-input"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setEmailError(null); // Clear error when user types
                }}
                className={cn("w-full", emailError && "border-destructive")}
                required
              />
              {emailError && (
                <p className={cn("text-destructive text-sm")}>{emailError}</p>
              )}
            </div>
            <Button
              type="submit"
              disabled={isPending || !email.trim()}
              className={cn("w-full")}
            >
              {isPending ? "Enviando..." : "Enviar"}
            </Button>
          </form>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default SignUpPlaystorePopover;
