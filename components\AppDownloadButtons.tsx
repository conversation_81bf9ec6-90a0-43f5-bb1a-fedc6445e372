"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { APP_CONFIG, getDownloadOptions } from "@/lib/app-config";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

type AppDownloadButtonsProps = {
  className?: string;
  variant?: "badges" | "text";
  showApk?: boolean;
  size?: "sm" | "md" | "lg";
};

const AppDownloadButtons = ({
  className,
  variant = "badges",
  showApk = true,
  size = "md",
}: AppDownloadButtonsProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const downloadOptions = getDownloadOptions();
  const filteredOptions = showApk
    ? downloadOptions
    : downloadOptions.filter((option) => option !== APP_CONFIG.apk);

  // Email validation for Gmail addresses
  const isValidGmail = (email: string): boolean => {
    const gmailRegex = /^[a-zA-Z0-9._%+-]+@gmail\.com$/;
    return gmailRegex.test(email);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isValidGmail(email)) {
      toast.error("Por favor, insira um endereço Gmail válido (@gmail.com)");
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success(
        "Solicitação enviada! Você receberá um email com o link de download da Play Store em breve.",
      );
      setEmail("");
      setIsPopoverOpen(false);
    } catch (error) {
      toast.error("Erro ao enviar solicitação. Tente novamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (variant === "text") {
    return (
      <div className={cn("flex flex-wrap gap-4", className)}>
        <Link
          href={APP_CONFIG.appStore.url}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center justify-center rounded-md bg-black px-4 py-2 text-white transition-colors hover:bg-gray-800"
        >
          App Store
        </Link>
        <Link
          href={APP_CONFIG.googlePlay.url}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700"
        >
          Google Play
        </Link>
        {showApk && (
          <Link
            href={APP_CONFIG.apk.url}
            className="inline-flex items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          >
            Download APK
          </Link>
        )}
      </div>
    );
  }

  const sizeClasses = {
    sm: "max-h-[2rem] max-w-[10rem]",
    md: "max-h-[3rem] max-w-[12rem]",
    lg: "max-h-[4rem] max-w-[16rem]",
  };

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {filteredOptions.map((option) => {
        // Special handling for Google Play button
        if (option === APP_CONFIG.googlePlay) {
          return (
            <Popover
              key={option.url}
              open={isPopoverOpen}
              onOpenChange={setIsPopoverOpen}
            >
              <PopoverTrigger asChild>
                <button
                  className={cn(
                    "flex cursor-pointer justify-center transition-transform hover:scale-105",
                    sizeClasses[size],
                  )}
                >
                  <Image
                    src={option.badge}
                    alt={option.alt}
                    width={192}
                    height={192}
                    className="h-full w-full object-contain"
                  />
                </button>
              </PopoverTrigger>
              <PopoverContent className={cn("w-80 p-4")} align="center">
                <div className={cn("space-y-4")}>
                  <div className={cn("space-y-2")}>
                    <h3 className={cn("text-lg font-semibold")}>
                      Acesso Beta - Google Play
                    </h3>
                    <p className={cn("text-muted-foreground text-sm")}>
                      Insira seu endereço Gmail para receber acesso ao teste
                      beta da nossa app na Play Store.
                    </p>
                  </div>
                  <form onSubmit={handleSubmit} className={cn("space-y-3")}>
                    <div className={cn("space-y-2")}>
                      <Label htmlFor="gmail-input">Endereço Gmail</Label>
                      <Input
                        id="gmail-input"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className={cn("w-full")}
                        required
                      />
                    </div>
                    <div className={cn("flex gap-2")}>
                      <Button
                        type="submit"
                        disabled={isSubmitting || !email}
                        className={cn("flex-1")}
                      >
                        {isSubmitting ? "Enviando..." : "Enviar"}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsPopoverOpen(false)}
                        className={cn("px-3")}
                      >
                        Cancelar
                      </Button>
                    </div>
                  </form>
                </div>
              </PopoverContent>
            </Popover>
          );
        }

        // Regular handling for other options
        return (
          <Link
            href={option.url}
            key={option.url}
            target={option.url.startsWith("http") ? "_blank" : "_self"}
            rel={
              option.url.startsWith("http") ? "noopener noreferrer" : undefined
            }
            className={cn(
              "flex justify-center transition-transform hover:scale-105",
              sizeClasses[size],
            )}
          >
            <Image
              src={option.badge}
              alt={option.alt}
              width={192}
              height={192}
              className="h-full w-full object-contain"
            />
          </Link>
        );
      })}
    </div>
  );
};

export default AppDownloadButtons;
