"use server";

import { z } from "zod";
import { sendEmail, createBetaSignupEmail } from "@/lib/mailer";

const betaSignupSchema = z.object({
  email: z
    .string()
    .email("Email inválido")
    .refine(
      (email) => email.endsWith("@gmail.com"),
      "Apenas endereços Gmail são aceitos (@gmail.com)",
    ),
});

type ActionResult = {
  success: boolean;
  message: string;
};

export async function submitBetaSignup(email: string): Promise<ActionResult> {
  try {
    const validatedData = betaSignupSchema.parse({ email });

    // Send single email to user with CC to admin
    const emailData = createBetaSignupEmail(validatedData.email);
    await sendEmail(emailData);

    return {
      success: true,
      message:
        "Solicitação enviada! Você receberá um email com o link de download da Play Store em breve.",
    };
  } catch (error) {
    console.error("Beta signup error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: error.errors[0]?.message || "Dados inválidos",
      };
    }

    return {
      success: false,
      message: "Erro ao enviar solicitação. Tente novamente.",
    };
  }
}
