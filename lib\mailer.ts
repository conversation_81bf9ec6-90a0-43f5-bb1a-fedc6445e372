import nodemailer from "nodemailer";
import { z } from "zod";

const emailEnvSchema = z.object({
  EMAIL_HOST: z.string().min(1, "EMAIL_HOST is required"),
  EMAIL_PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().positive()),
  EMAIL_USER: z.string().email("EMAIL_USER must be a valid email"),
  EMAIL_PASSWORD: z.string().min(1, "EMAIL_PASSWORD is required"),
  EMAIL_FROM: z.string().email("EMAIL_FROM must be a valid email"),
  EMAIL_FROM_NAME: z.string().min(1, "EMAIL_FROM_NAME is required"),
});

const emailDataSchema = z.object({
  to: z.string().email("Recipient email must be valid"),
  subject: z.string().min(1, "Subject is required"),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  html: z.string().min(1, "HTML content is required"),
  text: z.string().optional(),
});

type EmailData = z.infer<typeof emailDataSchema>;

const getEmailConfig = () => {
  try {
    return emailEnvSchema.parse({
      EMAIL_HOST: process.env.EMAIL_HOST,
      EMAIL_PORT: process.env.EMAIL_PORT,
      EMAIL_USER: process.env.EMAIL_USER,
      EMAIL_PASSWORD: process.env.EMAIL_PASSWORD,
      EMAIL_FROM: process.env.EMAIL_FROM,
      EMAIL_FROM_NAME: process.env.EMAIL_FROM_NAME,
    });
  } catch (error) {
    console.error("Email configuration validation failed:", error);
    throw new Error(
      "Invalid email configuration. Please check your environment variables.",
    );
  }
};

const createTransporter = () => {
  const config = getEmailConfig();

  return nodemailer.createTransport({
    host: config.EMAIL_HOST,
    port: config.EMAIL_PORT,
    secure: config.EMAIL_PORT === 465,
    auth: {
      user: config.EMAIL_USER,
      pass: config.EMAIL_PASSWORD,
    },
  });
};

export const sendEmail = async (emailData: EmailData): Promise<void> => {
  try {
    const validatedData = emailDataSchema.parse(emailData);
    const config = getEmailConfig();

    const transporter = createTransporter();

    const info = await transporter.sendMail({
      from: `"${config.EMAIL_FROM_NAME}" <${config.EMAIL_FROM}>`,
      to: validatedData.to,
      cc: validatedData.cc,
      subject: validatedData.subject,
      text: validatedData.text,
      html: validatedData.html,
    });

    console.log("Email sent successfully:", info.messageId);
  } catch (error) {
    console.error("Failed to send email:", error);
    throw new Error("Failed to send email. Please try again later.");
  }
};

export const createBetaSignupEmail = (userEmail: string) => ({
  to: userEmail,
  cc: "<EMAIL>",
  subject: "Zimbora - Acesso Beta Confirmado",
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #333; margin-bottom: 10px;">Zimbora</h1>
        <p style="color: #666; font-size: 16px;">A melhor plataforma de eventos de Luanda</p>
      </div>

      <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #28a745; margin-bottom: 20px;">🎉 Acesso Beta Confirmado!</h2>
        <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">
          Obrigado por se inscrever no programa beta do Zimbora! Sua solicitação foi recebida com sucesso.
        </p>
        <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">
          Em breve você receberá um convite por email para fazer o download da nossa app através da Google Play Store.
        </p>
        <p style="color: #333; line-height: 1.6;">
          Enquanto isso, fique atento ao seu email para não perder nenhuma novidade!
        </p>
      </div>

      <div style="background-color: #e9ecef; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="color: #495057; margin-bottom: 15px;">📋 Detalhes da Inscrição</h3>
        <p style="color: #333; margin-bottom: 8px;"><strong>Email:</strong> ${userEmail}</p>
        <p style="color: #333; margin-bottom: 8px;"><strong>Data:</strong> ${new Date().toLocaleString("pt-BR")}</p>
        <p style="color: #333; margin-bottom: 0;"><strong>Plataforma:</strong> Google Play Store</p>
      </div>

      <div style="text-align: center; color: #666; font-size: 14px;">
        <p>Equipe Zimbora</p>
        <p><EMAIL></p>
      </div>
    </div>
  `,
  text: `
    Zimbora - Acesso Beta Confirmado

    Obrigado por se inscrever no programa beta do Zimbora! Sua solicitação foi recebida com sucesso.

    Em breve você receberá um convite por email para fazer o download da nossa app através da Google Play Store.

    Enquanto isso, fique atento ao seu email para não perder nenhuma novidade!

    Detalhes da Inscrição:
    Email: ${userEmail}
    Data: ${new Date().toLocaleString("pt-BR")}
    Plataforma: Google Play Store

    Equipe Zimbora
    <EMAIL>
  `,
});
