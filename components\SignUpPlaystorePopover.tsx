"use client";

import { useState } from "react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

type SignUpPlaystorePopoverProps = {
  children: React.ReactNode;
  className?: string;
};

const SignUpPlaystorePopover = ({ 
  children, 
  className 
}: SignUpPlaystorePopoverProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Email validation for Gmail addresses
  const isValidGmail = (email: string): boolean => {
    const gmailRegex = /^[a-zA-Z0-9._%+-]+@gmail\.com$/;
    return gmailRegex.test(email);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isValidGmail(email)) {
      toast.error("Por favor, insira um endereço Gmail válido (@gmail.com)");
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      toast.success(
        "Solicitação enviada! Você receberá um email com o link de download da Play Store em breve.",
      );
      setEmail("");
      setIsPopoverOpen(false);
    } catch (error) {
      toast.error("Erro ao enviar solicitação. Tente novamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
      <PopoverTrigger asChild className={cn(className)}>
        {children}
      </PopoverTrigger>
      <PopoverContent className={cn("w-80 p-4")} align="center">
        <div className={cn("space-y-4")}>
          <div className={cn("space-y-2")}>
            <h3 className={cn("text-lg font-semibold")}>
              Acesso Beta - Google Play
            </h3>
            <p className={cn("text-muted-foreground text-sm")}>
              Insira seu endereço Gmail para receber acesso ao teste
              beta da nossa app na Play Store.
            </p>
          </div>
          <form onSubmit={handleSubmit} className={cn("space-y-3")}>
            <div className={cn("space-y-2")}>
              <Label htmlFor="gmail-input">Endereço Gmail</Label>
              <Input
                id="gmail-input"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={cn("w-full")}
                required
              />
            </div>
            <div className={cn("flex gap-2")}>
              <Button
                type="submit"
                disabled={isSubmitting || !email}
                className={cn("flex-1")}
              >
                {isSubmitting ? "Enviando..." : "Enviar"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsPopoverOpen(false)}
                className={cn("px-3")}
              >
                Cancelar
              </Button>
            </div>
          </form>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default SignUpPlaystorePopover;
