"use server";

import { z } from "zod";
import { sendEmail, createBetaSignupUserEmail, createBetaSignupNotificationEmail } from "@/lib/mailer";

const betaSignupSchema = z.object({
  email: z.string().email("Email inválido").refine(
    (email) => email.endsWith("@gmail.com"),
    "Apenas endereços Gmail são aceitos (@gmail.com)"
  ),
});

type ActionResult = {
  success: boolean;
  message: string;
};

export async function submitBetaSignup(email: string): Promise<ActionResult> {
  try {
    const validatedData = betaSignupSchema.parse({ email });
    
    const userEmail = createBetaSignupUserEmail(validatedData.email);
    await sendEmail(userEmail);
    
    const notificationEmail = createBetaSignupNotificationEmail(validatedData.email);
    
    await sendEmail(notificationEmail);
    
    return {
      success: true,
      message: "Solicitação enviada! Você receberá um email com o link de download da Play Store em breve.",
    };
  } catch (error) {
    console.error("Beta signup error:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: error.errors[0]?.message || "Dados inválidos",
        error: "VALIDATION_ERROR",
      };
    }
    
    return {
      success: false,
      message: "Erro ao enviar solicitação. Tente novamente.",
      error: "SERVER_ERROR",
    };
  }
}
